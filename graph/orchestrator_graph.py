import json
import os

from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.types import Overwrite

from agent_prompt.task_based_generation_agent import TASK_BASED_GENERATION_AGENT_PROMPT
from graph.task_based_generation_agent import create_task_based_generation_graph
from models.state import State
from utils.todo import add_todo, delete_todo, get_todos
from utils.tool_schema import (
    ADD_TODO_SCHEMA,
    DELETE_TODO_SCHEMA,
    GET_TODOS_SCHEMA,
    TASK_BASED_GENERATION_SCHEMA,
)


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        base_url="https://router.requesty.ai/v1",
        model="anthropic/claude-sonnet-4",
        api_key=REQUESTY_API_KEY,
    )
    llm = llm.bind_tools(
        [
            GET_TODOS_SCHEMA,
            ADD_TODO_SCHEMA,
            DELETE_TODO_SCHEMA,
            TASK_BASED_GENERATION_SCHEMA,
        ]
    )
    return llm


def agent_node(state: State):
    update = {}
    llm = get_llm()
    response = llm.invoke(state.workflow_generation_messages)
    update["workflow_generation_messages"] = [response]
    if (
        response.tool_calls
        and len(response.tool_calls) == 1
        and response.tool_calls[0]["name"] == "task_based_generation"
    ):
        for todo in state.todo:
            if todo["task_key"] == response.tool_calls[0]["args"]["task_key"]:
                break
        prompt = f"""
        task_key: {todo["task_key"]}
        title: {todo["title"]}
        details: {todo["details"]}
        plan: {todo["plan"]}
        workflow: {json.dumps(state.workflow_graph.get_graph_repr())}
"""
        update["task_based_generation_messages"] = Overwrite(
            [
                SystemMessage(content=TASK_BASED_GENERATION_AGENT_PROMPT),
                HumanMessage(content=prompt),
            ]
        )
    return update


def get_todos_node(state: State):
    update = {"workflow_generation_messages": []}
    tool_call = state.workflow_generation_messages[-1].tool_calls[0]

    try:
        todos = get_todos(state)
        update["workflow_generation_messages"].append(
            ToolMessage(content=json.dumps(todos), tool_call_id=tool_call["id"])
        )
    except Exception as e:
        error_msg = f"Error getting todos: {str(e)}"
        update["workflow_generation_messages"].append(
            ToolMessage(content=error_msg, tool_call_id=tool_call["id"])
        )

    return update


def add_todo_node(state: State):
    update = {"workflow_generation_messages": [], "todo": []}
    tool_call = state.workflow_generation_messages[-1].tool_calls[0]

    try:
        todo = add_todo(
            tool_call["args"]["task_key"],
            tool_call["args"]["title"],
            tool_call["args"]["details"],
            tool_call["args"]["plan"],
        )
        update["todo"].append(todo)
        update["workflow_generation_messages"].append(
            ToolMessage(
                content="Todo added successfully.", tool_call_id=tool_call["id"]
            )
        )
    except Exception as e:
        error_msg = f"Error adding todo: {str(e)}"
        update["workflow_generation_messages"].append(
            ToolMessage(content=error_msg, tool_call_id=tool_call["id"])
        )

    return update


def delete_todo_node(state: State):
    update = {"workflow_generation_messages": [], "todo": []}
    tool_call = state.workflow_generation_messages[-1].tool_calls[0]

    try:
        todo = delete_todo(tool_call["args"]["todo"])
        update["todo"].append(todo)
        update["workflow_generation_messages"].append(
            ToolMessage(
                content="Todo deleted successfully.", tool_call_id=tool_call["id"]
            )
        )
    except Exception as e:
        error_msg = f"Error deleting todo: {str(e)}"
        update["workflow_generation_messages"].append(
            ToolMessage(content=error_msg, tool_call_id=tool_call["id"])
        )

    return update


def router(state: State):
    tool_calls = state.workflow_generation_messages[-1].tool_calls
    if tool_calls:
        tool_name = tool_calls[0]["name"]
        if tool_name == "task_based_generation":
            return "task_based_generation"
        elif tool_name == "get_todos":
            return "get_todos"
        elif tool_name == "add_todo":
            return "add_todo"
        elif tool_name == "delete_todo":
            return "delete_todo"
        else:
            return "end"
    else:
        return "end"


def create_workflow_generation_graph(checkpointer=None):
    task_based_generation_graph = create_task_based_generation_graph(checkpointer=checkpointer)
    workflow_generation_graph = StateGraph(State)

    # Add all nodes
    workflow_generation_graph.add_node("agent", agent_node)
    workflow_generation_graph.add_node("get_todos", get_todos_node)
    workflow_generation_graph.add_node("add_todo", add_todo_node)
    workflow_generation_graph.add_node("delete_todo", delete_todo_node)
    workflow_generation_graph.add_node(
        "task_based_generation_agent", task_based_generation_graph
    )

    # Add conditional edges from agent to all tool nodes
    workflow_generation_graph.add_conditional_edges(
        "agent",
        router,
        {
            "get_todos": "get_todos",
            "add_todo": "add_todo",
            "delete_todo": "delete_todo",
            "task_based_generation": "task_based_generation_agent",
            "end": END,
        },
    )

    # Add edges from all tool nodes back to agent
    workflow_generation_graph.add_edge("get_todos", "agent")
    workflow_generation_graph.add_edge("add_todo", "agent")
    workflow_generation_graph.add_edge("delete_todo", "agent")
    workflow_generation_graph.add_edge("task_based_generation_agent", "agent")

    # Set entry point
    workflow_generation_graph.set_entry_point("agent")

    return workflow_generation_graph.compile(checkpointer=checkpointer)
